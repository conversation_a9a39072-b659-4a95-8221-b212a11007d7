/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

"use client"

import type { FC } from "react"
import { forwardRef } from "react"
import { usePostHogTracking } from "@/hooks/use-posthog-tracking"
import { LogOut, MoonIcon, SunIcon } from "lucide-react"
import { signOut } from "next-auth/react"
import { useTranslations } from "next-intl"
import { useTheme } from "next-themes"

import type { Session } from "@kreios/auth"
import { cn } from "@kreios/ui"
import { Avatar, AvatarFallback, AvatarImage } from "@kreios/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@kreios/ui/dropdown-menu"
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar } from "@kreios/ui/sidebar"
import { toast } from "@kreios/ui/sonner"

/**
 * Get initials from a name.
 */
const getAvatarString = (name: string) =>
  name
    .split(/ |\./)
    .slice(0, 2)
    .map((n) => n.at(0)?.toLocaleUpperCase())
    .join("")

const UserAvatar = forwardRef<
  React.ElementRef<typeof Avatar>,
  React.ComponentPropsWithoutRef<typeof Avatar> & { user: NonNullable<Session["user"]> }
>(({ className, user, ...props }, ref) => (
  <Avatar className={cn("h-8 w-8 rounded-lg", className)} {...props} ref={ref}>
    <AvatarImage referrerPolicy="no-referrer" src={user.image ?? undefined} alt={user.name ?? undefined} />
    <AvatarFallback className="rounded-lg">{getAvatarString(user.name ?? user.email ?? "U")}</AvatarFallback>
  </Avatar>
))

const UserLabel = ({ user }: { user: NonNullable<Session["user"]> }) => (
  <div className="grid flex-1 text-left text-sm leading-tight">
    <span className="truncate font-semibold">{user.name}</span>
    <span className="truncate text-xs">{user.email}</span>
  </div>
)

export const SidebarUserMenu: FC<{ user: NonNullable<Session["user"]> }> = ({ user }) => {
  const { isMobile, open } = useSidebar()
  const { theme, setTheme } = useTheme()
  const t = useTranslations("sidebar")
  const { trackButtonClick, trackEvent } = usePostHogTracking()

  // Track theme toggle
  const handleThemeToggle = () => {
    const newTheme = theme === "dark" ? "light" : "dark"
    
    trackButtonClick({
      button_variant: "ghost",
      button_size: "sm",
      button_text: t("theme"),
      button_location: "sidebar_user_menu",
      button_action: "toggle_theme",
      button_category: "settings",
      button_disabled: false,
    })

    trackEvent("theme_change", {
      previous_theme: theme,
      new_theme: newTheme,
      trigger_location: "sidebar_user_menu",
      user_email: user.email,
    })

    setTheme(newTheme)
  }

  // Track logout
  const handleLogout = () => {
    trackButtonClick({
      button_variant: "ghost",
      button_size: "sm", 
      button_text: t("logOut"),
      button_location: "sidebar_user_menu",
      button_action: "logout",
      button_category: "auth",
      button_disabled: false,
    })

    trackEvent("user_logout", {
      logout_method: "sidebar_menu",
      user_email: user.email,
      session_duration: Date.now(), // You could track actual session duration if needed
    })

    toast.promise(signOut(), {
      success: t("loggedOut"),
      loading: t("loggingOut"),
      error: t("logoutError"),
    })
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <UserAvatar user={user} />
              <UserLabel user={user} />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            side={isMobile || open ? "bottom" : "right"}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                <UserAvatar user={user} />
                <UserLabel user={user} />
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem onSelect={handleThemeToggle}>
                <SunIcon className="hidden h-[1.2rem] w-[1.2rem] dark:block" />
                <MoonIcon className="h-[1.2rem] w-[1.2rem] dark:hidden" />
                <span>{t("theme")}</span>
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />

            <DropdownMenuItem onSelect={handleLogout}>
              <LogOut />
              {t("logOut")}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
