/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

"use client"

import type { ComponentProps, FC } from "react"
import { usePostHogTracking } from "@/hooks/use-posthog-tracking"
import { signOut } from "next-auth/react"

import { Button } from "@kreios/ui/button"
import { toast } from "@kreios/ui/sonner"

interface TrackedLogoutButtonProps extends ComponentProps<typeof Button> {
  /**
   * Location where the logout button is placed
   * @example "header", "consent_page", "unauthorized_page"
   */
  location?: string
  /**
   * User email for tracking context
   */
  userEmail?: string
  /**
   * Custom logout method identifier
   */
  logoutMethod?: string
}

export const TrackedLogoutButton: FC<TrackedLogoutButtonProps> = ({ 
  children = "Logout", 
  location = "page",
  userEmail,
  logoutMethod = "button_click",
  onClick,
  ...props 
}) => {
  const { trackButtonClick, trackEvent } = usePostHogTracking()

  const handleLogout = (event: React.MouseEvent<HTMLButtonElement>) => {
    // Track logout button click
    trackButtonClick({
      button_variant: props.variant ?? "default",
      button_size: props.size ?? "default",
      button_text: typeof children === "string" ? children : "Logout",
      button_location: location,
      button_action: "logout",
      button_category: "auth",
      button_disabled: props.disabled ?? false,
    })

    // Track logout event
    trackEvent("user_logout", {
      logout_method: logoutMethod,
      logout_location: location,
      user_email: userEmail,
      session_duration: Date.now(), // You could track actual session duration if needed
    })

    // Call original onClick if provided
    onClick?.(event)

    // Perform logout
    toast.promise(signOut(), {
      success: "Logged out",
      loading: "Logging out...",
      error: "Failed to log out",
    })
  }

  return (
    <Button onClick={handleLogout} {...props}>
      {children}
    </Button>
  )
}

/**
 * Enhanced logout button for consent page
 */
export const ConsentPageLogoutButton: FC<Omit<TrackedLogoutButtonProps, "location" | "logoutMethod">> = (props) => (
  <TrackedLogoutButton 
    {...props} 
    location="consent_page" 
    logoutMethod="consent_page_logout"
  />
)

/**
 * Enhanced logout button for unauthorized page
 */
export const UnauthorizedPageLogoutButton: FC<Omit<TrackedLogoutButtonProps, "location" | "logoutMethod">> = (props) => (
  <TrackedLogoutButton 
    {...props} 
    location="unauthorized_page" 
    logoutMethod="unauthorized_page_logout"
  />
)

/**
 * Enhanced logout button for general layout
 */
export const GeneralLayoutLogoutButton: FC<Omit<TrackedLogoutButtonProps, "location" | "logoutMethod">> = (props) => (
  <TrackedLogoutButton 
    {...props} 
    location="general_layout" 
    logoutMethod="general_layout_logout"
  />
)
